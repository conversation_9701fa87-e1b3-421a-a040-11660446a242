<template>
  <div class="cyl-center">
    <!-- 头部列表 -->
    <div class="list-box">
      <span class="value-css">
        2025-09-18 【府河社区网格第一网格人员异常】    2025-09-18 【府河社区网格第一网格人员异常】
      </span>
    </div>

    <!-- 预警图例 -->
    <div class="warning-legend">
      <div class="legend-title">预警图例</div>
      <div class="legend-items">
        <div
          v-for="(item, index) in warningLegend"
          :key="index"
          class="legend-item"
          :class="{ active: item.visible }"
          @click="toggleLegend(index)"
        >
          <img class="legend-icon" :src="item.icon" />
          <span class="legend-text">{{ item.name }}</span>
          <span class="legend-count">({{ item.count }})</span>
          <input class="legend-check" type="checkbox" :checked="item.visible" @change.stop="toggleLegend(index)" />
        </div>
      </div>
    </div>

    <div class="map_center" id="ecartDiv" style="z-index: 9">
      <div ref="myEchart" class="map-echart"></div>
      <img
        ref="mapSvg"
        id="mapSvg"
        v-show="false"
        hidden
        src="@/pages/qyzfCyl/img/cyl/76801.jpg"
        @load="onMapImageLoad"
        crossorigin="anonymous"
      />
    </div>
  </div>
</template>


<script>
import { setAct } from '@/utils/util.js'
import bounds2d from '@/pages/qyzfCyl/json/anlu1.json'
import mapUrl from '@/pages/qyzfCyl/img/cyl/map_bg.png'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import * as echarts from 'echarts'
import redIcon from '@/pages/rkhl/img/slsw/红色预警.png'
import orangeIcon from '@/pages/rkhl/img/slsw/橙色预警.png'
import yellowIcon from '@/pages/rkhl/img/slsw/黄色预警.png'
import popupBg from '@/pages/fxyjfx/img/bg.png'
export default {
  name: 'fxyjfxCenter',
  data() {
    return {
      mapImageLoaded: false,
      listDataTotal: [
        {
          name: '实时人口',
          value: '0',
          unit: '人',
          zs: '-',
        },
        {
          name: '常住人口',
          value: '0',
          unit: '人',
          zs: '-',
        },
        {
          name: '户籍人口',
          value: '0',
          unit: '人',
          zs: '-',
        },
      ],
      listData: [
        {
          name: '规上企业数',
          value: '0',
          unit: '家',
          zs: '-',
        },
        {
          name: '规上企业产值',
          value: '0',
          unit: '亿元',
          zs: '-',
        },
        // {
        //   name: '培育指数',
        //   value: '-',
        //   unit: '',
        //   zs: '-'
        // }
      ],
      cylIndex: -1,
      animationData: [
        // {
        //   city: '安陆市',
        //   num: 10,
        //   point: [113.632, 31.291],
        // },
        {
          city: '王义贞镇',
          num: 24578,
          point: [113.379, 31.346],
        },
        {
          city: '孛畈镇',
          num: 24578,
          point: [113.505, 31.382],
        },
        {
          city: '烟店镇',
          num: 24578,
          point: [113.606, 31.323],
        },
        {
          city: '雷公镇',
          num: 24578,
          point: [113.476, 31.296],
        },
        {
          city: '洑水镇',
          num: 24578,
          point: [113.704, 31.348],
        },
        {
          city: '接官乡',
          num: 24578,
          point: [113.744, 31.414],
        },
        {
          city: '赵棚镇',
          num: 24578,
          point: [113.848, 31.394],
        },

        {
          city: '陈店乡',
          num: 24578,
          point: [113.805, 31.289],
        },
        {
          city: '木梓乡',
          num: 24578,
          point: [113.529, 31.223],
        },
        {
          city: '棠棣镇',
          num: 24578,
          point: [113.615, 31.241],
        },
        {
          city: '李店镇',
          num: 24578,
          point: [113.778, 31.22],
        },

        {
          city: '开发区',
          num: 24578,
          point: [113.713, 31.285],
        },
        {
          city: '府城街道',
          num: 24578,
          point: [113.682, 31.276],
        },
        {
          city: '南城街道',
          num: 24578,
          point: [113.688, 31.202],
        },
        {
          city: '巡店镇',
          num: 24578,
          point: [113.568, 31.155],
        },
        {
          city: '辛榨乡',
          num: 24578,
          point: [113.633, 31.111],
        },
      ],
      myChartMap: null,
      time: null,
      hjPerson: '0000000', //户籍人口
      pcPerson: '0000000', //普查人口
      populationTotal: '0000000', //实时人口
      wlPerson: '0000000', // 流动人员
      hjldPerson: '0000000', //户籍、流动人口
      ldPerson: '0000000', //流动人口
      initObj: {
        hjPerson: '0000000', //户籍人口
        pcPerson: '0000000', //普查人口
        populationTotal: '0000000', //实时人口
        wlPerson: '0000000', // 流动人员
        hjldPerson: '0000000', //户籍、流动人口
        ldPerson: '0000000' //流动人口
      },
      cityName: '金华市',
      cityCode: '330700',
      // 图例与点位
      warningLegend: [
        { name: '红色预警', level: 'red', visible: true, count: 0, icon: redIcon },
        { name: '橙色预警', level: 'orange', visible: true, count: 0, icon: orangeIcon },
        { name: '黄色预警', level: 'yellow', visible: true, count: 0, icon: yellowIcon },
      ],
      warningPoints: [
        // { id: 1, name: '洑水镇', level: 'yellow', type: '人员异常聚集', address: '洑水镇', time: '2025-9-18 20:14:57', currentCount: 785, avgCount: 37, exceedPercent: 21232.0, coord: [113.704, 31.348] },
        // { id: 2, name: '王义贞镇', level: 'red', type: '人员异常聚集', address: '王义贞镇', time: '2025-9-18 19:45:10', currentCount: 1250, avgCount: 45, exceedPercent: 2677.78, coord: [113.379, 31.346] },
        // { id: 3, name: '孛畈镇', level: 'orange', type: '人员异常聚集', address: '孛畈镇', time: '2025-9-18 19:30:25', currentCount: 890, avgCount: 52, exceedPercent: 1611.54, coord: [113.505, 31.382] },
        // { id: 4, name: '雷公镇', level: 'yellow', type: '人员异常聚集', address: '雷公镇', time: '2025-9-18 18:45:12', currentCount: 650, avgCount: 38, exceedPercent: 1610.53, coord: [113.476, 31.296] },
      ],
      detailGraphicId: 'warning-detail-graphic'
    }
  },

  mounted() {
    // 延迟初始化，确保DOM完全渲染
    this.$nextTick(() => {
      this.getdata()
    })
    window.closePop = () => {
      this.removeDetailGraphic()
    }
  },

  methods: {
    /**
     * 将地图缩放并居中到指定坐标点，并可选显示临时标记
     * 坐标格式：WGS84(经纬度) 数组或对象
     * - 数组: [lng, lat] 例如 [113.704, 31.348]
     * - 对象: { lng: 113.704, lat: 31.348 }
     * @param {Array|Object} coordinate 坐标
     * @param {number} zoom 缩放等级(建议 0.8 - 20)，默认 1.8
     * @param {boolean} showMarker 是否显示临时标记，默认 true
     */
    focusToCoordinate(coordinate, zoom = 1.8, showMarker = false) {
      if (!this.myChartMap || !coordinate) return
      let lngLat
      if (Array.isArray(coordinate) && coordinate.length >= 2) {
        lngLat = [Number(coordinate[0]), Number(coordinate[1])]
      } else if (coordinate && typeof coordinate === 'object') {
        lngLat = [Number(coordinate.lng), Number(coordinate.lat)]
      }
      if (!lngLat || !isFinite(lngLat[0]) || !isFinite(lngLat[1])) return

      // 更新 geo 的中心与缩放
      const option = this.myChartMap.getOption()
      if (option.geo && option.geo[0]) {
        option.geo[0].center = lngLat
        option.geo[0].zoom = Number(zoom) || option.geo[0].zoom || 1.2
      }

      // 临时标记（可关闭）
      const markerId = 'focus-marker'
      const markerSeries = {
        id: markerId,
        type: 'effectScatter',
        coordinateSystem: 'geo',
        zlevel: 1200,
        rippleEffect: { period: 4, brushType: 'stroke', scale: 3 },
        symbol: 'pin',
        symbolSize: 60,
        itemStyle: { color: '#3fdaff' },
        data: showMarker ? [{ name: '定位', value: lngLat }] : []
      }

      // 注入或更新标记
      const series = option.series || []
      const idx = series.findIndex(s => s && s.id === markerId)
      if (idx >= 0) series[idx] = markerSeries
      else series.push(markerSeries)
      option.series = series

      this.myChartMap.setOption(option, { notMerge: false, lazyUpdate: false })
    },
    cleanMap() {
      // ECharts 5+ 优化：检查地图底图是否可用
      const mapImage = this.$refs.mapSvg
      let areaColorConfig

      if (mapImage && mapImage.complete && mapImage.naturalWidth > 0) {
        // 图片已加载成功，使用图片作为背景
        areaColorConfig = {
          image: mapImage,
          repeat: 'no-repeat'
        }
      } else {
        // 图片未加载或加载失败，使用备用方案
        areaColorConfig = 'rgba(0, 50, 100, 0.8)'
        console.warn('地图底图未加载，使用备用颜色方案')
      }

      let newOption = JSON.parse(JSON.stringify(this.myChartMap.getOption()))
      newOption.series = [
        {
          type: 'map',
          map: 'jh',
          zoom: 1.2,
          roam: false,
          layoutCenter: ['50%', '50%'],
          layoutSize: '90%',
          aspectScale: 1,
          selectedMode: false,
          label: {
            //初始标签样式
            show: true,
            color: '#fff',
            position: 'inside',
            distance: 0,
            fontSize: 50,
          },
          itemStyle: {
            areaColor: areaColorConfig,
            borderColor: '#70e8e9',
            borderWidth: 3,
            emphasis: {
              label: {
                show: true,
                color: '#fff',
                fontSize: 50,
              },
              areaColor: areaColorConfig,
              borderColor: '#3fdaff',
              borderWidth: 10,
            },
          },
          zlevel: 99,
          data: [],
        },
      ]
      this.myChartMap.setOption(newOption, { notMerge: true })
    },

    getdata() {
      getCsdnInterface1('/csrk_ssrsldrs', { addressCode: this.cityCode }).then((res) => {
        this.listDataTotal[0].value = res.data.data[0].population_count
      })
      getCsdnInterface1('/csrk_czrk_hjrk').then(res => {
        this.listDataTotal[1].value = res.data.data[0].rs
        this.listDataTotal[2].value = res.data.data[1].rs
      })

      getCsdnInterface1('/rkhl_zdqy_wg_yj').then((res) => {
        this.warningPoints = res.data.data
        this.updateWarningCounts()
                // 初次绘制点位
        this.updateWarningSeries()
      })
      echarts.registerMap('jh', bounds2d)
      this.myChartMap = echarts.init(this.$refs.myEchart)

    },

    initEcharts() {
      this.$nextTick(() => {
        // ECharts 5+ 优化：检查地图底图是否可用
        const mapImage = this.$refs.mapSvg
        let areaColorConfig

        if (mapImage && mapImage.complete && mapImage.naturalWidth > 0) {
          // 图片已加载成功，使用图片作为背景
          areaColorConfig = {
            image: mapImage,
            repeat: 'no-repeat'
          }
        }
        let option = {
          tooltip: {
            show: false,
          },
          geo: [
            {
              type: 'map',
              map: 'jh',
              zoom: 1.2,
              roam: true,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 1,
              selectedMode: false,
              // 优化的标签配置
              label: {
                show: true,
                color: '#fff',
                position: 'inside',
                distance: 0,
                fontSize: 50,
                fontWeight: 'bold',
                textShadowColor: 'rgba(0, 0, 0, 0.8)',
                textShadowBlur: 3,
                textShadowOffsetX: 1,
                textShadowOffsetY: 1
              },
              // 使用图片底图的样式配置
              itemStyle: {
                areaColor: areaColorConfig,
                borderColor: '#70e8e9',
                borderWidth: 3,
                // ECharts 5+ 优化的阴影效果
                shadowColor: 'rgba(112, 232, 233, 0.3)',
                shadowBlur: 8,
                shadowOffsetX: 2,
                shadowOffsetY: 2,
                emphasis: {
                  label: {
                    show: true,
                    color: '#fff',
                    fontSize: 52,
                    fontWeight: 'bold',
                    textShadowColor: 'rgba(0, 0, 0, 0.9)',
                    textShadowBlur: 5
                  },
                  areaColor: areaColorConfig,
                  borderColor: '#3fdaff',
                  borderWidth: 10,
                  // 悬停时的阴影效果
                  shadowColor: 'rgba(63, 218, 255, 0.5)',
                  shadowBlur: 15,
                  shadowOffsetX: 3,
                  shadowOffsetY: 3
                }
              },
              zlevel: 99,
              data: [],
            },
          ],
          series: [], // 数据
        }

        // ECharts 5+ 优化的选项设置
        this.myChartMap.setOption(option, {
          notMerge: false,
          lazyUpdate: false,
          silent: false
        })

        // 清除之前的事件监听器
        this.myChartMap.off('click')

        // 添加窗口大小变化监听器以确保响应式
        this.addResizeListener()

        // 注册点击点位事件
        this.myChartMap.on('click', this.handleMapClick)
      })
    },

    // echartsMapClick(e) {
    //   if (e.data) {
    //     // console.log('地图点击', e.data, this.cylList[this.cylIndex])
    //     let cyl = this.cylList[this.cylIndex] ? this.cylList[this.cylIndex].cylId : ''
    //     let qx = e.data.name
    //     this.queryQyList(cyl, qx)
    //   }
    // },

    // queryQyList(cyl, qx) {
    //   window.parent.lay.openIframe({
    //     type: 'openIframe',
    //     name: 'qyzf-qy-list',
    //     src: baseURL.url + '/static/citybrain/qyhx/commont/qyzf-qy-list.html',
    //     left: 'calc(50% - 1554px)',
    //     top: '190px',
    //     width: '3108px',
    //     height: '1900px',
    //     zIndex: '993',
    //     argument: {
    //       status: 'qyfbQyList',
    //       type: cyl,
    //       name: qx,
    //     },
    //   })
    // },

    // 地图图片加载成功
    onMapImageLoad() {
      console.log('地图底图加载成功')
      this.mapImageLoaded = true
      // 如果ECharts已经初始化，重新设置选项以应用图片
      if (this.myChartMap) {
        this.initEcharts()
      }
    },


    // ECharts 5+ 优化的响应式处理
    addResizeListener() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
      }

      // 使用 ResizeObserver 替代 window.resize 事件以获得更好的性能
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          if (this.myChartMap) {
            this.myChartMap.resize()
          }
        })
        this.resizeObserver.observe(this.$refs.myEchart)
      } else {
        // 降级方案：使用防抖的 resize 事件
        this.resizeHandler = this.debounce(() => {
          if (this.myChartMap) {
            this.myChartMap.resize()
          }
        }, 300)
        window.addEventListener('resize', this.resizeHandler)
      }
    },

    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 性能优化：批量更新数据
    updateMapData(newData) {
      if (!this.myChartMap || !newData) return

      // 使用 ECharts 5+ 的增量更新功能
      const option = {
        series: [{
          id: 'mapSeries',
          data: newData
        }]
      }

      this.myChartMap.setOption(option, {
        notMerge: false,
        lazyUpdate: true,
        silent: true
      })
    },

    // 图例计数
    updateWarningCounts() {
      this.warningLegend.forEach((l) => {
        l.count = this.warningPoints.filter((p) => p.level === l.level).length
      })
    },

    // 切换图例
    toggleLegend(index) {
      this.warningLegend[index].visible = !this.warningLegend[index].visible
      this.updateWarningSeries()
    },

    // 根据勾选绘制点位
    updateWarningSeries() {
      if (!this.myChartMap) return
      const visibleLevels = new Set(this.warningLegend.filter((l) => l.visible).map((l) => l.level))
      const points = JSON.parse(JSON.stringify(this.warningPoints))
      points.filter((p) => visibleLevels.has(p.level))
      points.map((point) => {
        point.zb = point.zb.split(",").map(Number)
      })

      const iconMap = { red: redIcon, orange: orangeIcon, yellow: yellowIcon }
      const series = {
        type: 'scatter',
        coordinateSystem: 'geo',
        zlevel: 1000,
        symbolSize: 100,
        data: points.map((p) => ({
          name: p.name,
          value: p.zb,
          symbol: 'image://' + iconMap[p.level],
          warningData: p,
        })),
        label: { show: false },
        emphasis: { scale: 1.1 }
      }

      const option = this.myChartMap.getOption()
      const idx = option.series.findIndex((s) => s.zlevel === 1000 && s.type === 'scatter')
      if (idx >= 0) option.series[idx] = series
      else option.series.push(series)
      this.myChartMap.setOption(option, { notMerge: false })
    },

    // 点击地图点位
    handleMapClick(params) {
      // if (!params.data || !params.data.warningData) {
      //   return
      // }
      // const warn = params.data.warningData
      // const coordPixel = this.myChartMap.convertToPixel({ geoIndex: 0 }, warn.coord)
      // const lines = [
      //   `预警详情`,
      //   `预警详情：${warn.type}`,
      //   `预警地址：${warn.address}`,
      //   `预警时间：${warn.time}`,
      //   `时刻人数：${warn.currentCount}人（超值${warn.exceedPercent}%）`,
      //   `七日均值：${warn.avgCount}`,
      //   `预警等级：${this.getLevelText(warn.level)}`,
      // ]

      // const graphic = this.buildDetailGraphic(coordPixel[0], coordPixel[1], lines)
      // this.removeDetailGraphic()
      // this.myChartMap.setOption({ graphic })
    },

    getLevelText(level) {
      return level === 'red' ? '红色预警' : level === 'orange' ? '橙色预警' : '黄色预警'
    },

    buildDetailGraphic(x, y, lines) {
      const width = 520
      const lineHeight = 34
      const paddingX = 26
      const paddingY = 26
      const height = paddingY * 2 + lines.length * lineHeight - 20
      const left = x + 30
      const top = y - height - 28
      const closeSize = 22
      return {
        elements: [
          // 背景图片
          {
            id: this.detailGraphicId + '-bg-img',
            type: 'image',
            left,
            top,
            style: {
              image: popupBg,
              width,
              height
            },
            z: 100000,
            zlevel: 1000,
            silent: true
          },
          // 关闭按钮
          {
            id: this.detailGraphicId + '-close',
            type: 'text',
            left: left + width - paddingX - closeSize,
            top: top + 10,
            style: { text: '×', fill: '#cfe9ff', font: 'bold 24px Arial', cursor: 'pointer' },
            z: 100002,
            zlevel: 1002,
            onclick: () => { this.removeDetailGraphic()}
          },
          // 文本行
          ...lines.map((t, i) => ({
            id: this.detailGraphicId + '-t-' + i,
            type: 'text',
            left: left + paddingX,
            top: top + paddingY + i * lineHeight - 10,
            style: { text: t, fill: '#cfe9ff', font: '18px \u5FAE\u8F6F\u96C5\u9ED1' },
            z: 100001,
            zlevel: 1001,
            silent: true
          }))
        ]
      }
    },

    removeDetailGraphic() {
      var chartOption = this.myChartMap.getOption();
      chartOption.graphic = [];
      this.myChartMap.setOption(chartOption, true);
    }
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }

    // 清理 resize 事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }

    // 销毁 ECharts 实例
    if (this.myChartMap) {
      this.myChartMap.dispose()
      this.myChartMap = null
    }
  },
}
</script>

<style scoped>
.map_center {
  width: 3360px;
  height: 1900px;
  position: relative;
  background-size: 100% 100%;
  /* 确保容器有明确的尺寸 */
  min-width: 3360px;
  min-height: 1900px;
}

.map-echart {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  /* 确保ECharts容器正确渲染 */
  position: relative;
  z-index: 1;
}

.list-box {
  width: 2230px;
  height: 100px;
  position: absolute;
  left:50%;
  top: 32px;
  background-color: #0E6275;
  padding: 5px;
  transform: translateX(-50%) skew(20deg);
}


.cursor {
  cursor: pointer;
}

.value-css {
  margin-top: 15px;
  padding-left: 20px;
  font-size: 48px;
  font-weight: bold;
  display: inline-block;
  background: white;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  transform: skew(-20deg);
}

.icon {
  width: 40px;
  height: 33px;
  background: url('@/pages/qyzfCyl/img/cyl/down.png') no-repeat 0px 0px;
}

.cyl-list {
  position: absolute;
  left: 100px;
  top: 100px;
  z-index: 99;
}

.cyl-list-item {
  width: 300px;
  /* height:100px; */
  background: url('@/pages/qyzfCyl/img/cyl/cyl_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 30px 30px;
  box-sizing: border-box;
  margin-bottom: 40px;
  cursor: pointer;
}

.active {
  background: url('@/pages/qyzfCyl/img/cyl/cyl_active.png') no-repeat;
  background-size: 100% 100%;
}

.cyl-list-item > p {
  font-size: 32px;
  color: #fff;
  white-space: wrap;
  line-height: 50px;
}

/* 预警图例样式 */
.warning-legend {
  position: absolute;
  bottom: 200px;
  right: 400px;
  z-index: 1000;
  padding: 18px 20px;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(0, 40, 80, 0.85) 0%, rgba(0, 25, 55, 0.85) 100%);
  border: 1px solid rgba(112, 232, 233, 0.35);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.35);
  backdrop-filter: blur(6px);
  min-width: 260px;
}

.legend-title {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 12px;
  letter-spacing: 1px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.legend-item:hover {
  background: rgba(112, 232, 233, 0.08);
  border-color: rgba(112, 232, 233, 0.28);
}

.legend-item.active {
  background: rgba(63, 218, 255, 0.15);
  border-color: #3fdaff;
}

.legend-icon {
  width: 34px;
  height: 26px;
  margin-right: 10px;
  display: inline-block;
}

.legend-text {
  color: #ffffff;
  font-size: 22px;
  margin-right: 6px;
}

.legend-count {
  color: #bad3ff;
  font-size: 18px;
}

.legend-check {
  margin-left: auto;
  transform: scale(1.2);
  cursor: pointer;
}

</style>